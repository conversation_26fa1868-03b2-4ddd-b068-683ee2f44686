<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>保护使能 - 桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="logo.png">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #1a2332 0%, #2d3748 100%);
            color: #ffffff;
            width: 1920px;
            height: 1080px;
            overflow: hidden;
            position: relative;
        }

        /* 科技感背景 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 153, 204, 0.08) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 15px;
        }

        .header {
            text-align: center;
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 2px solid rgba(0, 212, 255, 0.4);
        }

        .header h1 {
            font-size: 28px;
            color: #00d4ff;
            text-shadow: 0 0 15px rgba(0, 212, 255, 0.5);
            font-weight: bold;
        }

        .main-content {
            flex: 1;
            display: flex;
            gap: 15px;
            overflow: hidden;
        }

        .protection-panel {
            flex: 1;
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
            padding: 15px;
            overflow: hidden;
            min-width: 0; /* 防止内容溢出影响平分 */
        }

        .panel-title {
            font-size: 18px;
            color: #00d4ff;
            text-align: center;
            margin-bottom: 15px;
            padding: 8px 0;
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            font-weight: bold;
        }

        .params-table {
        width: 100%;
        border-collapse: collapse;
        font-size: 18px; /* 适度缩小字体 */
    }

        .params-table th {
        background: rgba(0, 212, 255, 0.3);
        color: #00d4ff;
        padding: 8px 6px; /* 适度缩小内边距 */
        text-align: center;
        border: 1px solid rgba(0, 212, 255, 0.4);
        font-weight: bold;
        font-size: 16px; /* 适度缩小表头字体 */
    }

        .params-table td {
        padding: 6px 8px; /* 适度缩小内边距 */
        text-align: center;
        border: 1px solid rgba(0, 212, 255, 0.2);
        background: rgba(42, 49, 66, 0.7);
        vertical-align: middle;
        height: 46px; /* 适度缩小行高 */
    }

        .params-table tr:hover td {
            background: rgba(42, 49, 66, 0.9);
            border-color: rgba(0, 212, 255, 0.4);
        }

        .param-index {
        font-size: 16px; /* 适度缩小序号字体 */
        color: #7a8ba0;
        font-weight: bold;
        width: 42px; /* 适度缩小宽度 */
        text-align: right;
        padding-right: 8px;
    }

        .param-name {
        font-size: 15px; /* 适度缩小参数名称字体 */
        color: #ffffff;
        text-align: left;
        padding-left: 6px;
        line-height: 1.3; /* 适度缩小行高 */
        max-width: 200px;
        word-wrap: break-word;
        overflow: visible;
        white-space: normal;
    }

        .param-current {
        font-size: 16px; /* 适度缩小当前值字体 */
        color: #00d4ff;
        font-weight: bold;
        width: 55px; /* 适度缩小宽度 */
    }

        .param-setting {
            width: 60px; /* 增大设置列宽度 */
        }

        .toggle-switch {
            position: relative;
            width: 60px; /* 增大开关宽度 */
            height: 30px; /* 增大开关高度 */
            background: #dc3545;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 212, 255, 0.3);
            margin: 0 auto;
        }

        .toggle-switch.active {
            background: #007bff;
        }

        .toggle-switch::before {
            content: '';
            position: absolute;
            top: 3px;
            left: 3px;
            width: 24px; /* 增大滑块 */
            height: 24px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .toggle-switch.active::before {
            transform: translateX(30px); /* 调整滑块位置 */
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>保护使能</h1>
        </div>

        <div class="main-content">
            <div class="protection-panel">
                <div class="panel-title">保护功能使能1</div>
                <table class="params-table">
                    <thead>
                        <tr>
                        <th>序号</th>
                        <th>参数名称</th>
                        <th>设定值</th>
                        <th>当前值</th>
                        <th>序号</th>
                        <th>参数名称</th>
                        <th>设定值</th>
                        <th>当前值</th>
                    </tr>
                    </thead>
                    <tbody id="protection-table-1">
                        <!-- 保护使能参数1-32将在这里动态生成 -->
                    </tbody>
                </table>
            </div>

            <div class="protection-panel">
                <div class="panel-title">保护功能使能2</div>
                <table class="params-table">
                    <thead>
                        <tr>
                        <th>序号</th>
                        <th>参数名称</th>
                        <th>设定值</th>
                        <th>当前值</th>
                        <th>序号</th>
                        <th>参数名称</th>
                        <th>设定值</th>
                        <th>当前值</th>
                    </tr>
                    </thead>
                    <tbody id="protection-table-2">
                        <!-- 保护使能参数33-64将在这里动态生成 -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        /**
         * 保护使能配置管理类
         * 负责数据处理和界面更新
         */
        class ProtectionEnableManager {
            constructor() {
                this.protectionParams = [];
                this.currentValues = {};
                this.modifiedValues = {};

                this.initializeParams();
                this.createParamTables();
            }

            /**
             * 初始化保护使能参数定义
             * 根据图片中的参数配置
             */
            initializeParams() {
                // 根据图片中显示的64个参数
                const paramNames = [
                    // 第一组 (1-32)
                    '电网线电压有效值Ⅰ段过压报警使能', '电网线电压有效值Ⅱ段过压保护使能', '电网线电压有效值Ⅲ段过压保护使能', '电网线电压瞬时值过压保护使能',
                    '电网线电压有效值Ⅰ段欠压报警使能', '电网线电压有效值Ⅱ段欠压保护使能', '电网线电压有效值Ⅲ段欠压保护使能', '电网线电压有效值不平衡保护使能',
                    '电网线电压缺相保护使能', '电网电压反序保护使能', '同步信号丢失保护使能', 'SVG侧霍尔传感器故障保护使能',
                    '模拟板采样通道自检故障保护使能', '零序电压超标保护使能', 'SVG输出电流有效值Ⅰ段过流报警使能', 'SVG输出电流有效值Ⅱ段过流保护使能',
                    'SVG输出电流瞬时值过流保护使能', 'SVG输出电流硬件过流保护使能', 'SVG输出电流缺相保护使能', 'SVG输出电流指令限幅报警使能',
                    'SVG瞬时电流过流(CT检测方式)故障保护使能', 'PT故障保护使能', 'SVG零序电流故障保护使能', '功率单元状态一般性故障使能',
                    '功率单元UDC过压保护使能', '功率单元UDC不平衡保护使能', '功率单元硬件保护(HW)使能', '功率单元自检故障保护使能',
                    '功率单元状态不一致保护使能', 'RS485通信超时故障使能', 'RS485通信校验故障使能', 'DRAM读超时故障使能',

                    // 第二组 (33-64)
                    'DRAM故障使能', 'DRAM写参数故障使能', 'WDI看门狗复位保护使能', '充电超时故障使能',
                    '行程开关未合故障使能', '充电接触器K1不吸合使能', '充电接触器K1不分开使能', '断路器QF1不吸合使能',
                    '断路器QF1不分开使能', 'CAN通信发送超时使能', 'CAN通信接收超时使能', '水冷系统电源故障使能',
                    '读铁电参数错误使能', '变压器超温报警使能', '变压器超温跳闸使能', '变压器轻瓦斯报警使能',
                    '变压器重瓦斯跳闸使能', '变压器压力报警使能', '变压器压力跳闸使能', '风机缺相保护使能',
                    '单元短路故障使能', '瞬时过流2跳闸使能', '电网低电压1保护使能', '电网低电压2保护使能',
                    '自动恢复失败次数超限保护使能', '故障复位超时保护使能', '柜间光纤断保护使能', '水冷系统运行状态异常使能',
                    '水冷系统综合故障使能', '水冷系统报警使能', '水冷系统请求停止使能', '高速光纤通信故障使能'
                ];

                // 创建64个参数
                for (let i = 0; i < 64; i++) {
                    const group = i < 32 ? 1 : 2;
                    const displayIndex = (i % 32) + 1; // 每组都从1开始编号

                    this.protectionParams.push({
                        id: `param_${i + 1}`,
                        index: displayIndex,
                        name: paramNames[i],
                        currentValue: Math.floor(Math.random() * 2), // 随机初始值
                        settingValue: Math.floor(Math.random() * 2), // 随机设定值
                        group: group
                    });
                }
            }

            /**
             * 创建参数表格界面
             */
            createParamTables() {
                const table1 = document.getElementById('protection-table-1');
                const table2 = document.getElementById('protection-table-2');

                // 创建第一组参数（1-32），每行显示两个参数
                for (let i = 0; i < 16; i++) {
                    const param1 = this.protectionParams[i * 2];
                    const param2 = this.protectionParams[i * 2 + 1];
                    const row = this.createParamTableRow(param1, param2);
                    table1.appendChild(row);
                }

                // 创建第二组参数（33-64），每行显示两个参数
                for (let i = 0; i < 16; i++) {
                    const param1 = this.protectionParams[32 + i * 2];
                    const param2 = this.protectionParams[32 + i * 2 + 1];
                    const row = this.createParamTableRow(param1, param2);
                    table2.appendChild(row);
                }
            }

            /**
             * 创建单个参数表格行（包含两个参数）
             */
            createParamTableRow(param1, param2) {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="param-index">${param1.index}</td>
                    <td class="param-name">${param1.name}</td>
                    <td class="param-setting">
                        <div class="toggle-switch ${param1.settingValue ? 'active' : ''}"
                             id="toggle-${param1.id}"
                             onclick="protectionManager.toggleParam('${param1.id}')">
                        </div>
                    </td>
                    <td class="param-current" id="current-${param1.id}">${param1.currentValue}</td>
                    <td class="param-index">${param2.index}</td>
                    <td class="param-name">${param2.name}</td>
                    <td class="param-setting">
                        <div class="toggle-switch ${param2.settingValue ? 'active' : ''}"
                             id="toggle-${param2.id}"
                             onclick="protectionManager.toggleParam('${param2.id}')">
                        </div>
                    </td>
                    <td class="param-current" id="current-${param2.id}">${param2.currentValue}</td>
                `;
                return row;
            }

            /**
             * 切换参数设定值
             */
            toggleParam(paramId) {
                const param = this.protectionParams.find(p => p.id === paramId);
                if (!param) return;

                // 切换设定值
                param.settingValue = param.settingValue ? 0 : 1;

                // 更新界面
                const toggle = document.getElementById(`toggle-${paramId}`);
                if (param.settingValue) {
                    toggle.classList.add('active');
                } else {
                    toggle.classList.remove('active');
                }

                // 记录修改
                this.modifiedValues[paramId] = param.settingValue;

                console.log(`参数 ${param.name} 设定值已修改为: ${param.settingValue}`);
            }

        }

        // 全局变量
        let protectionManager;

        /**
         * 页面初始化
         */
        document.addEventListener('DOMContentLoaded', function() {
            console.log('保护使能配置页面初始化...');
            protectionManager = new ProtectionEnableManager();
            console.log('保护使能配置页面初始化完成');
        });
    </script>
</body>
</html>
