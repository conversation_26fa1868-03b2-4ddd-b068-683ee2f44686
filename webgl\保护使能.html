<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>保护使能配置 - 桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="logo.png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #0a0e1a 0%, #1a1f2e 100%);
            color: #ffffff;
            width: 1920px;
            height: 1080px;
            overflow: hidden;
            position: relative;
        }

        /* 科技感背景动画 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 153, 204, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(102, 224, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .container {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px 0;
            border-bottom: 2px solid rgba(0, 212, 255, 0.3);
        }

        .header h1 {
            font-size: 36px;
            color: #00d4ff;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }

        .header .subtitle {
            font-size: 18px;
            color: #b8c5d6;
            opacity: 0.8;
        }

        .status-bar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            background: rgba(26, 31, 46, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #dc3545;
            animation: pulse 2s infinite;
        }

        .status-indicator.connected {
            background: #28a745;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .main-content {
            flex: 1;
            display: flex;
            gap: 20px;
            overflow: hidden;
        }

        .protection-panel {
            flex: 1;
            background: rgba(26, 31, 46, 0.9);
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            overflow-y: auto;
        }

        .panel-title {
            font-size: 24px;
            color: #00d4ff;
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(0, 212, 255, 0.3);
        }

        .params-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 3px;
            max-height: calc(100vh - 200px);
            overflow-y: auto;
        }

        .param-row {
            display: grid;
            grid-template-columns: 30px 1fr 50px 50px 50px;
            gap: 8px;
            align-items: center;
            padding: 4px 8px;
            background: rgba(42, 49, 66, 0.6);
            border: 1px solid rgba(0, 212, 255, 0.2);
            border-radius: 4px;
            transition: all 0.3s ease;
            min-height: 32px;
        }

        .param-row:hover {
            border-color: #00d4ff;
            background: rgba(42, 49, 66, 0.8);
        }

        .param-index {
            font-size: 12px;
            color: #7a8ba0;
            text-align: center;
            font-weight: bold;
        }

        .param-name {
            font-size: 12px;
            color: #ffffff;
            line-height: 1.2;
            word-wrap: break-word;
            word-break: break-all;
            white-space: normal;
            max-height: 36px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .param-current {
            text-align: center;
            font-size: 12px;
            color: #00d4ff;
            font-weight: bold;
        }

        .param-setting {
            text-align: center;
        }

        .toggle-switch {
            position: relative;
            width: 36px;
            height: 18px;
            background: #dc3545;
            border-radius: 9px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 212, 255, 0.3);
        }

        .toggle-switch.active {
            background: #28a745;
        }

        .toggle-switch::before {
            content: '';
            position: absolute;
            top: 1px;
            left: 1px;
            width: 14px;
            height: 14px;
            background: white;
            border-radius: 50%;
            transition: all 0.3s ease;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .toggle-switch.active::before {
            transform: translateX(18px);
        }

        .param-value {
            text-align: center;
            font-size: 12px;
            color: #ffffff;
        }

        .control-panel {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 20px;
            z-index: 1000;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: 2px solid rgba(0, 123, 255, 0.5);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
            border: 2px solid rgba(40, 167, 69, 0.5);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        /* 滚动条样式 */
        .protection-panel::-webkit-scrollbar {
            width: 8px;
        }

        .protection-panel::-webkit-scrollbar-track {
            background: rgba(26, 31, 46, 0.5);
            border-radius: 4px;
        }

        .protection-panel::-webkit-scrollbar-thumb {
            background: rgba(0, 212, 255, 0.5);
            border-radius: 4px;
        }

        .protection-panel::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 212, 255, 0.8);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-shield-alt"></i> 保护使能</h1>
            <div class="subtitle">SVG数字化系统保护功能使能配置界面</div>
        </div>

        <div class="status-bar">
            <div class="status-item">
                <div class="status-indicator" id="mqtt-status"></div>
                <span>MQTT连接状态: <span id="connection-text">未连接</span></span>
            </div>
            <div class="status-item">
                <i class="fas fa-clock"></i>
                <span>最后更新: <span id="last-update">--</span></span>
            </div>
            <div class="status-item">
                <i class="fas fa-database"></i>
                <span>数据接收: <span id="data-count">0</span> 条</span>
            </div>
            <div class="status-item">
                <i class="fas fa-edit"></i>
                <span>已修改: <span id="modified-count">0</span> 项</span>
            </div>
        </div>

        <div class="main-content">
            <div class="protection-panel">
                <div class="panel-title">保护功能使能1</div>
                <div class="params-grid" id="protection-grid-1">
                    <!-- 保护使能参数1-32将在这里动态生成 -->
                </div>
            </div>

            <div class="protection-panel">
                <div class="panel-title">保护功能使能2</div>
                <div class="params-grid" id="protection-grid-2">
                    <!-- 保护使能参数1-32将在这里动态生成（序号重新从1开始） -->
                </div>
            </div>
        </div>

        <div class="control-panel">
            <button class="btn btn-primary" onclick="connectMQTT()">
                <i class="fas fa-plug"></i> 连接MQTT
            </button>
            <button class="btn btn-success" onclick="downloadConfig()">
                <i class="fas fa-download"></i> 下载配置
            </button>
        </div>
    </div>

    <!-- 引入MQTT库 -->
    <script src="https://unpkg.com/mqtt@4.3.7/dist/mqtt.min.js"></script>
    <script>
        /**
         * 保护使能配置管理类
         * 负责MQTT通信、数据处理和界面更新
         */
        class ProtectionEnableManager {
            constructor() {
                this.mqttClient = null;
                this.isConnected = false;
                this.dataCount = 0;
                this.modifiedCount = 0;
                this.protectionParams = [];
                this.currentValues = {};
                this.modifiedValues = {};

                // 使用与main.html相同的MQTT配置
                this.mqttConfig = {
                    username: 'FastBee',
                    password: 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjdhM2RjZWY1LTE5ODgtNDg4OS04OTAzLTIwY2I0YjIyZDA0YSJ9.g1HCISIvQd6YkNgWhblKnXqHeRI74lnP1F8qZOd9XV5a7J41Qi77f9jLxxWd_EVN0XJPP1haYeRK3Uz_xrbEuA',
                    cleanSession: true,
                    keepAlive: 30,
                    clientId: 'web-protection-' + Math.random().toString(16).substr(2),
                    connectTimeout: 60000,
                    url: 'wss://mqtt.qizhiyun.cc/mqtt',
                    subscribeTopic: '/189/D19QBHKRZ791U/ws/service',
                    publishTopic: '/189/D19QBHKRZ791U/property/post'
                };

                this.initializeParams();
                this.createParamGrids();
                this.updateStatusDisplay();
            }

            /**
             * 初始化保护使能参数定义
             * 使用CSV文件中的参数数据
             */
            initializeParams() {
                // 基于CSV文件的参数定义（64个参数）
                const csvParams = [
                    { id: '2101_GOA1', name: '电网线电压有效值 Ⅰ 段过压报警使能' },
                    { id: '2101_GOP2', name: '电网线电压有效值 Ⅱ 段过压保护使能' },
                    { id: '2101_GOP3', name: '电网线电压幅值 Ⅲ 段过压保护使能' },
                    { id: '2101_GIOP', name: '电网线电压瞬时值过压保护使能' },
                    { id: '2101_GUA1', name: '电网线电压有效值 Ⅰ 段欠压报警使能' },
                    { id: '2101_GUP2', name: '电网线电压有效值 Ⅱ 段欠压保护使能' },
                    { id: '2101_GUP3', name: '电网线电压幅值 Ⅲ 段欠压保护使能' },
                    { id: '2101_GVIP', name: '电网线电压有效值不平衡保护使能' },
                    { id: '2101_GVPLP', name: '电网线电压缺相保护使能' },
                    { id: '2101_GVRSP', name: '电网电压反序保护使能' },
                    { id: '2101_SSLP', name: '同步信号丢失保护使能' },
                    { id: '2101_SHSFP', name: 'SVG 侧霍尔传感器故障保护使能' },
                    { id: '2101_ABSSCFP', name: '模拟板采样通道自检故障保护使能' },
                    { id: '2101_ZSVOP', name: '零序电压超标保护使能' },
                    { id: '2101_SOCOA1', name: 'SVG 输出电流有效值 Ⅰ 段过流报警使能' },
                    { id: '2101_SOCOP2', name: 'SVG 输出电流有效值 Ⅱ 段过流保护使能' },
                    { id: '2101_SOCIOP', name: 'SVG 输出电流瞬时值过流保护使能' },
                    { id: '2101_SOCHOP', name: 'SVG 输出电流硬件过流 (HW) 保护使能' },
                    { id: '2101_SOCPLP', name: 'SVG 输出电流缺相保护使能' },
                    { id: '2101_SOCCLA', name: 'SVG 输出电流指令限幅报警使能' },
                    { id: '2101_SICOCDFP', name: 'SVG 瞬时电流过流（CT 检测方式）故障保护使能' },
                    { id: '2101_PTFP', name: 'PT 故障保护使能' },
                    { id: '2101_SZSCFP', name: 'SVG 零序电流故障保护使能' },
                    { id: '2101_PUGF', name: '功率单元状态一般性故障 (SW) 使能' },
                    { id: '2101_PUUOP', name: '功率单元 UDC 过压保护 (SW) 使能' },
                    { id: '2101_PUUIP', name: '功率单元 UDC 不平衡保护 (SW) 使能' },
                    { id: '2101_PUHP', name: '功率单元硬件保护 (HW) 使能' },
                    { id: '2101_PUSCFP', name: '功率单元自检故障保护使能' },
                    { id: '2101_PUSIP', name: '功率单元状态不一致保护使能' },
                    { id: '2101_RS485CTF', name: 'RS485 通信超时故障使能' },
                    { id: '2101_RS485CCF', name: 'RS485 通信校验故障使能' },
                    { id: '2101_DRTF', name: 'DRAM 读超时故障使能' },
                    { id: '2102_DF', name: 'DRAM 故障使能' },
                    { id: '2102_DWPF', name: 'DRAM 写参数故障使能' },
                    { id: '2102_WWRP', name: 'WDI 看门狗复位保护使能' },
                    { id: '2102_CTF', name: '充电超时故障使能' },
                    { id: '2102_TSNCSDF', name: '行程开关未合故障使能 / 烟感故障使能' },
                    { id: '2102_CCK1NE', name: '充电接触器 K1 不吸合使能' },
                    { id: '2102_CCK1ND', name: '充电接触器 K1 不分开使能' },
                    { id: '2102_CBQF1NE', name: '断路器 QF1 不吸合使能' },
                    { id: '2102_CBQF1ND', name: '断路器 QF1 不分开使能' },
                    { id: '2102_CCST', name: 'CAN 通信发送超时使能' },
                    { id: '2102_CCRT', name: 'CAN 通信接收超时使能' },
                    { id: '2102_WCSPF', name: '水冷系统电源故障使能' },
                    { id: '2102_RFPE', name: '读铁电参数错误使能' },
                    { id: '2102_TOA', name: '变压器超温报警使能' },
                    { id: '2102_TOT', name: '变压器超温跳闸使能' },
                    { id: '2102_TLGA', name: '变压器轻瓦斯报警使能' },
                    { id: '2102_THGT', name: '变压器重瓦斯跳闸使能' },
                    { id: '2102_TPA', name: '变压器压力报警使能' },
                    { id: '2102_TPT', name: '变压器压力跳闸使能' },
                    { id: '2102_FPLP', name: '风机缺相保护使能' },
                    { id: '2102_USCF', name: '单元短路故障使能' },
                    { id: '2102_IOC2T', name: '瞬时过流 2 跳闸使能' },
                    { id: '2102_GLV1P', name: '电网低电压 1 保护使能' },
                    { id: '2102_GLV2P', name: '电网低电压 2 保护使能' },
                    { id: '2102_ARFCLP', name: '自动恢复失败次数超限保护使能' },
                    { id: '2102_FRTP', name: '故障复位超时保护使能' },
                    { id: '2102_ICFBP', name: '柜间光纤断保护使能' },
                    { id: '2102_WCSOSA', name: '水冷系统运行状态异常使能' },
                    { id: '2102_WCSCF', name: '水冷系统综合故障使能' },
                    { id: '2102_WCSA', name: '水冷系统报警使能' },
                    { id: '2102_WCSRS', name: '水冷系统请求停止使能' },
                    { id: '2102_HSFCF', name: '高速光纤通信故障使能' }
                ];

                // 创建64个参数，分为两组显示
                for (let i = 0; i < csvParams.length; i++) {
                    const param = csvParams[i];
                    const group = i < 32 ? 1 : 2;
                    const displayIndex = (i % 32) + 1; // 每组都从1开始编号

                    this.protectionParams.push({
                        id: param.id,
                        index: displayIndex,
                        name: param.name,
                        currentValue: 0,
                        settingValue: 0,
                        group: group,
                        bit_definition: {
                            bit: 0,
                            type: "single"
                        }
                    });
                }
            }

            /**
             * 创建参数网格界面
             */
            createParamGrids() {
                const grid1 = document.getElementById('protection-grid-1');
                const grid2 = document.getElementById('protection-grid-2');

                // 创建第一组参数（1-32）
                this.protectionParams.slice(0, 32).forEach(param => {
                    const paramRow = this.createParamRow(param);
                    grid1.appendChild(paramRow);
                });

                // 创建第二组参数（33-64）
                this.protectionParams.slice(32, 64).forEach(param => {
                    const paramRow = this.createParamRow(param);
                    grid2.appendChild(paramRow);
                });
            }

            /**
             * 创建单个参数行
             */
            createParamRow(param) {
                const row = document.createElement('div');
                row.className = 'param-row';
                row.innerHTML = `
                    <div class="param-index">${param.index}</div>
                    <div class="param-name" title="${param.name}">${param.name}</div>
                    <div class="param-current" id="current-${param.id}">${param.currentValue}</div>
                    <div class="param-setting">
                        <div class="toggle-switch ${param.settingValue ? 'active' : ''}"
                             id="toggle-${param.id}"
                             onclick="protectionManager.toggleParam('${param.id}')">
                        </div>
                    </div>
                    <div class="param-value" id="value-${param.id}">${param.settingValue}</div>
                `;
                return row;
            }

            /**
             * 切换参数设定值
             */
            toggleParam(paramId) {
                const param = this.protectionParams.find(p => p.id === paramId);
                if (!param) return;

                // 切换设定值
                param.settingValue = param.settingValue ? 0 : 1;

                // 更新界面
                const toggle = document.getElementById(`toggle-${paramId}`);
                const valueDisplay = document.getElementById(`value-${paramId}`);

                if (param.settingValue) {
                    toggle.classList.add('active');
                } else {
                    toggle.classList.remove('active');
                }

                valueDisplay.textContent = param.settingValue;

                // 记录修改
                this.modifiedValues[paramId] = param.settingValue;
                this.updateModifiedCount();

                console.log(`参数 ${param.name} 设定值已修改为: ${param.settingValue}`);
            }

            /**
             * 连接MQTT服务器
             * 使用与main.html相同的连接方式
             */
            connectMQTT() {
                if (this.isConnected) {
                    console.log('MQTT已连接');
                    alert('MQTT已连接');
                    return;
                }

                console.log('正在连接MQTT服务器...');

                return new Promise((resolve, reject) => {
                    try {
                        // 使用与main.html相同的MQTT配置
                        const options = {
                            username: this.mqttConfig.username,
                            password: this.mqttConfig.password,
                            cleanSession: this.mqttConfig.cleanSession,
                            keepAlive: this.mqttConfig.keepAlive,
                            clientId: this.mqttConfig.clientId,
                            connectTimeout: this.mqttConfig.connectTimeout,
                        };

                        console.log('连接到 MQTT 服务器：', this.mqttConfig.url);

                        // 使用全局 mqtt 对象连接
                        if (typeof mqtt !== 'undefined') {
                            this.mqttClient = mqtt.connect(this.mqttConfig.url, options);
                        } else {
                            throw new Error('MQTT 客户端库未加载');
                        }

                        this.mqttClient.on('connect', () => {
                            console.log('MQTT 保护使能连接成功');
                            this.isConnected = true;
                            this.updateStatusDisplay();
                            alert('MQTT连接成功');

                            // 订阅数据主题
                            this.mqttClient.subscribe(this.mqttConfig.subscribeTopic, { qos: 1 }, (err) => {
                                if (err) {
                                    console.error('订阅失败:', err);
                                } else {
                                    console.log('成功订阅主题:', this.mqttConfig.subscribeTopic);
                                }
                            });

                            resolve();
                        });

                        this.mqttClient.on('message', (topic, message) => {
                            this.handleMQTTMessage(topic, message);
                        });

                        this.mqttClient.on('error', (error) => {
                            console.error('MQTT 保护使能连接失败:', error);
                            this.isConnected = false;
                            this.updateStatusDisplay();
                            reject(error);
                        });

                        this.mqttClient.on('close', () => {
                            console.log('MQTT 保护使能连接已断开');
                            this.isConnected = false;
                            this.updateStatusDisplay();
                        });

                    } catch (error) {
                        console.error('MQTT 连接配置错误:', error);
                        reject(error);
                    }
                });
            }

            /**
             * 处理MQTT消息
             * 支持新的消息格式
             */
            handleMQTTMessage(topic, message) {
                try {
                    const data = JSON.parse(message.toString());
                    console.log('收到MQTT消息:', data);

                    this.dataCount++;

                    // 支持新的消息格式
                    if (data.message && Array.isArray(data.message)) {
                        // 处理message数组格式
                        data.message.forEach(item => {
                            if (item.id && item.value !== undefined) {
                                this.updateParameterValue(item.id, item.value, item.ts);
                            }
                        });
                    } else if (data.properties) {
                        // 处理properties对象格式
                        Object.keys(data.properties).forEach(propId => {
                            const prop = data.properties[propId];
                            this.updateParameterValue(propId, prop.value, prop.time);
                        });
                    } else if (data.id && data.value !== undefined) {
                        // 处理单个参数格式
                        this.updateParameterValue(data.id, data.value, data.ts);
                    }

                    this.updateStatusDisplay();

                } catch (error) {
                    console.error('消息处理错误:', error);
                }
            }

            /**
             * 更新单个参数值
             */
            updateParameterValue(paramId, value, timestamp) {
                const param = this.protectionParams.find(p => p.id === paramId);
                if (param) {
                    param.currentValue = parseInt(value) || 0;
                    this.currentValues[paramId] = param.currentValue;

                    // 更新界面显示
                    const currentDisplay = document.getElementById(`current-${paramId}`);
                    if (currentDisplay) {
                        currentDisplay.textContent = param.currentValue;
                    }

                    console.log(`参数更新: ${param.name} = ${param.currentValue}`);
                }
            }



            /**
             * 下载配置到设备
             * 使用新的消息格式
             */
            downloadConfig() {
                if (!this.isConnected) {
                    alert('请先连接MQTT服务器');
                    return;
                }

                if (Object.keys(this.modifiedValues).length === 0) {
                    alert('没有修改的参数需要下载');
                    return;
                }

                // 为每个修改的参数创建单独的消息
                const messages = [];
                Object.keys(this.modifiedValues).forEach(paramId => {
                    const param = this.protectionParams.find(p => p.id === paramId);
                    if (param) {
                        const message = {
                            id: paramId,
                            ts: new Date().toISOString().replace('T', ' ').replace('Z', '').substring(0, 23),
                            value: this.modifiedValues[paramId].toString(),
                            writeValue: this.modifiedValues[paramId].toString(),
                            parent_svg_id: "SVG_2101", // 根据实际情况调整
                            bit_definition: param.bit_definition || {
                                bit: 0,
                                type: "single"
                            }
                        };
                        messages.push(message);
                    }
                });

                // 发送所有配置消息
                let successCount = 0;
                let errorCount = 0;

                messages.forEach((message, index) => {
                    const messageStr = JSON.stringify(message);
                    console.log(`发送配置消息 ${index + 1}/${messages.length}:`, messageStr);

                    this.mqttClient.publish(this.mqttConfig.publishTopic, messageStr, { qos: 1 }, (err) => {
                        if (err) {
                            console.error(`配置下载失败 (${message.id}):`, err);
                            errorCount++;
                        } else {
                            console.log(`配置下载成功 (${message.id})`);
                            successCount++;
                        }

                        // 检查是否所有消息都已处理
                        if (successCount + errorCount === messages.length) {
                            if (errorCount === 0) {
                                alert(`配置下载成功！已发送 ${successCount} 个参数的修改`);
                                // 清空修改记录
                                this.modifiedValues = {};
                                this.updateModifiedCount();
                            } else {
                                alert(`配置下载部分成功：成功 ${successCount} 个，失败 ${errorCount} 个`);
                            }
                        }
                    });
                });
            }

            /**
             * 更新修改计数
             */
            updateModifiedCount() {
                this.modifiedCount = Object.keys(this.modifiedValues).length;
                document.getElementById('modified-count').textContent = this.modifiedCount;
            }

            /**
             * 更新状态显示
             */
            updateStatusDisplay() {
                const statusIndicator = document.getElementById('mqtt-status');
                const connectionText = document.getElementById('connection-text');
                const lastUpdate = document.getElementById('last-update');
                const dataCount = document.getElementById('data-count');

                if (this.isConnected) {
                    statusIndicator.classList.add('connected');
                    connectionText.textContent = '已连接';
                } else {
                    statusIndicator.classList.remove('connected');
                    connectionText.textContent = '未连接';
                }

                lastUpdate.textContent = new Date().toLocaleTimeString();
                dataCount.textContent = this.dataCount;
            }
        }

        // 全局变量和函数
        let protectionManager;

        /**
         * 页面初始化
         */
        document.addEventListener('DOMContentLoaded', function() {
            console.log('保护使能配置页面初始化...');
            protectionManager = new ProtectionEnableManager();
            console.log('保护使能配置页面初始化完成');
        });

        /**
         * 连接MQTT（全局函数）
         */
        function connectMQTT() {
            protectionManager.connectMQTT();
        }

        /**
         * 下载配置（全局函数）
         */
        function downloadConfig() {
            protectionManager.downloadConfig();
        }

        // 监听来自父窗口的消息（用于测试）
        window.addEventListener('message', function(event) {
            if (event.data.type === 'mqtt-data') {
                console.log('收到测试数据:', event.data.data);
                if (protectionManager) {
                    protectionManager.handleMQTTMessage('test', JSON.stringify(event.data.data));
                }
            }
        });

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            if (protectionManager && protectionManager.mqttClient) {
                protectionManager.mqttClient.end();
            }
        });
    </script>
</body>
</html>
