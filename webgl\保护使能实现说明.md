# 保护使能页面实现说明

## 项目概述

根据提供的图片设计要求，完全重新实现了"保护使能"配置页面，严格按照图片中的布局、颜色方案、控件类型和功能要求进行开发。

## 实现特点

### ✅ 完全按照图片设计
- **页面标题**: "保护使能"，居中显示，青色字体
- **布局结构**: 左右两个面板并排显示
- **面板标题**: "保护功能使能1" 和 "保护功能使能2"
- **表格结构**: 每行显示两个参数，包含序号、参数名称、设定值、当前值
- **颜色方案**: 深蓝色背景，青色边框和标题，与项目整体风格一致

### ✅ 精确的尺寸和布局
- **页面尺寸**: 1366×768像素，适合iframe弹窗显示
- **响应式设计**: 针对目标分辨率优化，无滚动条
- **紧凑布局**: 64个参数完美适配页面空间
- **表格设计**: 7列表格结构，序号、参数名称、设定值、当前值重复显示

### ✅ 交互控件实现
- **开关控件**: 圆形滑动开关，红色表示关闭，蓝色表示开启
- **参数编号**: 两个面板都从1-32重新编号
- **参数名称**: 完整显示，支持自动换行
- **当前值显示**: 青色字体，突出显示实时数据

### ✅ 数据结构
- **64个保护参数**: 涵盖电网保护、SVG保护、通信保护等各类功能
- **参数分组**: 前32个参数显示在左侧面板，后32个参数显示在右侧面板
- **随机初始值**: 模拟真实设备状态，便于测试交互功能

## 文件结构

```
webgl/
├── 保护使能.html              # 主页面文件
├── 保护使能测试.html          # 测试环境页面
└── 保护使能实现说明.md        # 本说明文档
```

## 技术实现

### HTML结构
- 使用语义化HTML5标签
- 表格结构清晰，便于维护
- 响应式容器布局

### CSS样式
- 深色科技主题，与项目整体风格一致
- 精确的尺寸控制和间距设置
- 平滑的动画过渡效果
- 自定义开关控件样式

### JavaScript功能
- 面向对象的参数管理类
- 动态表格生成
- 开关状态切换
- 参数数据管理

## 参数列表

### 保护功能使能1 (1-32)
1. 电网线电压有效值Ⅰ段过压报警使能
2. 电网线电压有效值Ⅱ段过压保护使能
3. 电网线电压有效值Ⅲ段过压保护使能
4. 电网线电压瞬时值过压保护使能
5. 电网线电压有效值Ⅰ段欠压报警使能
6. 电网线电压有效值Ⅱ段欠压保护使能
7. 电网线电压有效值Ⅲ段欠压保护使能
8. 电网线电压有效值不平衡保护使能
9. 电网线电压缺相保护使能
10. 电网电压反序保护使能
... (共32个参数)

### 保护功能使能2 (1-32)
1. DRAM故障使能
2. DRAM写参数故障使能
3. WDI看门狗复位保护使能
4. 充电超时故障使能
5. 行程开关未合故障使能
6. 充电接触器K1不吸合使能
7. 充电接触器K1不分开使能
8. 断路器QF1不吸合使能
... (共32个参数)

## 使用方法

### 1. 直接访问
```
http://localhost:8000/webgl/保护使能.html
```

### 2. iframe集成
```html
<iframe src="保护使能.html" width="1366" height="768"></iframe>
```

### 3. 弹窗显示
```javascript
showModuleModal('protection-enable', '保护使能', 'fas fa-shield-alt', '保护使能.html');
```

## 测试验证

使用 `保护使能测试.html` 页面进行功能测试：
- 开关控件交互测试
- 参数显示验证
- 布局响应性检查
- 视觉效果确认

## 集成说明

该页面已完全按照图片要求实现，可以直接集成到现有的Unity WebGL项目中：
- 与main.html的弹窗系统兼容
- 遵循项目的设计规范和颜色主题
- 支持1366×768像素的标准弹窗尺寸
- 无需额外的依赖库或配置

## 更新日志

### v1.0.0 (2025-01-08)
- ✨ 完全按照图片要求重新实现页面
- 🎨 精确匹配设计稿的布局和样式
- 🔧 优化表格结构和参数显示
- 📱 适配1366×768像素弹窗尺寸
- 🎯 实现64个保护参数的完整配置界面
