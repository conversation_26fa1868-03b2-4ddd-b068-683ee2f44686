<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>保护使能测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .iframe-container {
            width: 1366px;
            height: 768px;
            border: 2px solid #ccc;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px auto;
            transform: scale(0.8);
            transform-origin: top center;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .btn {
            padding: 10px 20px;
            margin: 0 10px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .info h3 {
            margin-top: 0;
            color: #0066cc;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>保护使能页面测试</h1>
        
        <div class="info">
            <h3>测试说明</h3>
            <p>这是保护使能页面的测试环境。页面已按照图片要求完全重新实现：</p>
            <ul>
                <li>✅ 1366×768像素尺寸，适合iframe弹窗显示</li>
                <li>✅ 深蓝色科技主题，与项目整体风格一致</li>
                <li>✅ 左右两个面板布局，每个面板32个参数</li>
                <li>✅ 表格形式显示，每行包含两个参数</li>
                <li>✅ 圆形开关控件，蓝色表示开启状态</li>
                <li>✅ 参数编号从1-32重新开始</li>
                <li>✅ 参数名称和当前值显示</li>
            </ul>
        </div>

        <div class="controls">
            <button class="btn btn-primary" onclick="refreshIframe()">刷新页面</button>
            <button class="btn btn-success" onclick="openInNewWindow()">新窗口打开</button>
        </div>

        <div class="iframe-container">
            <iframe id="testFrame" src="保护使能.html"></iframe>
        </div>

        <div class="info">
            <h3>功能测试</h3>
            <p>请在上方的iframe中测试以下功能：</p>
            <ul>
                <li>点击开关控件切换参数设定值</li>
                <li>观察开关状态变化（红色→蓝色）</li>
                <li>检查参数名称显示是否完整</li>
                <li>验证表格布局是否与图片一致</li>
                <li>确认两个面板的参数编号都是1-32</li>
            </ul>
        </div>
    </div>

    <script>
        /**
         * 刷新iframe
         */
        function refreshIframe() {
            const iframe = document.getElementById('testFrame');
            iframe.src = iframe.src;
        }

        /**
         * 在新窗口中打开
         */
        function openInNewWindow() {
            window.open('保护使能.html', '_blank', 'width=1366,height=768');
        }

        // 监听iframe加载完成
        document.getElementById('testFrame').onload = function() {
            console.log('保护使能页面加载完成');
        };
    </script>
</body>
</html>
